{% extends "base.html" %}

{% block title %}All Invoices - Invoice Generator 2025{% endblock %}

{% block extra_styles %}
/* Filter controls */
.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.filter-group select, .filter-group input {
    padding: 0.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    background-color: var(--input-background);
    color: var(--text-primary);
}

/* Summary stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--accent-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
}

.stat-content p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Table styles */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    font-weight: 600;
    color: var(--text-secondary);
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(var(--accent-color-rgb), 0.05);
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.action-btn i {
    margin-right: 0.25rem;
}

.action-btn-view {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
}

.action-btn-view:hover {
    background-color: var(--primary-color);
    color: white;
}

.action-btn-delete {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    color: var(--danger-color);
}

.action-btn-delete:hover {
    background-color: var(--danger-color);
    color: white;
}

/* Sortable table */
.sortable {
    cursor: pointer;
    position: relative;
    user-select: none;
}

.sortable i {
    margin-left: 5px;
    font-size: 0.8em;
    opacity: 0.5;
    transition: all 0.2s ease;
}

.sortable.asc i {
    opacity: 1;
    transform: rotate(180deg);
}

.sortable.desc i {
    opacity: 1;
    transform: rotate(0deg);
}

.sortable:hover i {
    opacity: 0.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
    }
    
    .summary-stats {
        grid-template-columns: 1fr;
    }
    
    .mobile-hide {
        display: none;
    }
}
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-file-invoice"></i> All Invoices</h1>
        <p class="page-subtitle">Manage and view all your invoices for {{ selected_year }}</p>
    </div>
    <div class="page-actions">
        <a href="{{ url_for('index') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Invoice
        </a>
    </div>
</div>

<!-- Year selector -->
<div class="year-selector">
    <label for="year-select">Select Year:</label>
    <select id="year-select" onchange="window.location.href='/all_invoices?year=' + this.value">
        {% for year in available_years %}
        <option value="{{ year }}" {% if year == selected_year %}selected{% endif %}>{{ year }}</option>
        {% endfor %}
    </select>
</div>

<!-- Summary stats -->
<div class="summary-stats">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-file-invoice"></i>
        </div>
        <div class="stat-content">
            <h3>{{ invoices|length }}</h3>
            <p>Total Invoices</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-money-bill-wave"></i>
        </div>
        <div class="stat-content">
            <h3>{{ currency_symbol }}{{ "%.2f"|format(total_amount) }}</h3>
            <p>Total Amount</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
            <h3>{{ unique_clients }}</h3>
            <p>Unique Clients</p>
        </div>
    </div>
</div>

<!-- Filter controls -->
<div class="filter-controls">
    <div class="filter-group">
        <label for="client-filter">Filter by Client:</label>
        <select id="client-filter">
            <option value="all">All Clients</option>
            {% for client_name in client_names %}
            <option value="{{ client_name }}">{{ client_name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="filter-group">
        <label for="month-filter">Filter by Month:</label>
        <select id="month-filter">
            <option value="all">All Months</option>
            <option value="01">January</option>
            <option value="02">February</option>
            <option value="03">March</option>
            <option value="04">April</option>
            <option value="05">May</option>
            <option value="06">June</option>
            <option value="07">July</option>
            <option value="08">August</option>
            <option value="09">September</option>
            <option value="10">October</option>
            <option value="11">November</option>
            <option value="12">December</option>
        </select>
    </div>
    <div class="filter-group">
        <label for="search-filter">Search:</label>
        <input type="text" id="search-filter" placeholder="Search invoices...">
    </div>
</div>

<!-- Invoices table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table sortable-table" id="invoices-table">
                <thead>
                    <tr>
                        <th class="sortable" data-sort="invoice">Invoice # <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="date">Date <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="client">Client <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="service">Service <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="amount">Amount <i class="fas fa-sort"></i></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr data-client="{{ invoice[3] }}" data-month="{{ invoice[2].split('/')[1] }}">
                        <td data-value="{{ invoice[1] }}">{{ invoice[1] }}</td>
                        <td data-value="{{ invoice[2] }}" data-date="{{ invoice[2].split('/')[2] + invoice[2].split('/')[1] + invoice[2].split('/')[0] }}">{{ invoice[2] }}</td>
                        <td data-value="{{ invoice[3] }}">{{ invoice[3] }}</td>
                        <td data-value="{{ invoice[4] }}">{{ invoice[4] }}</td>
                        <td data-value="{{ invoice[6] }}">
                            {{ invoice[10] }}{{ "%.2f"|format(invoice[6]) }}
                            {% if invoice[7] %}<small>+VAT</small>{% endif %}
                            {% if invoice[8] %}<small>-IRPF</small>{% endif %}
                        </td>
                        <td>
                            <div class="table-actions">
                                <a href="/view_invoice/{{ invoice[1] }}" class="action-btn action-btn-view">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="#" onclick="confirmDeleteInvoice('{{ invoice[1] }}')" class="action-btn action-btn-delete">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize page-specific functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize table filters using the generic function
        const filterConfig = [
            { filterId: 'client-filter', attribute: 'client', type: 'select' },
            { filterId: 'month-filter', attribute: 'month', type: 'select' },
            { filterId: 'search-filter', attribute: 'search', type: 'search' }
        ];

        // Add custom stats update callback
        filterConfig.updateStatsCallback = function() {
            const table = document.getElementById('invoices-table');
            const rows = table.querySelectorAll('tbody tr');
            const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');

            // Update total invoices stat
            document.querySelector('.stat-card:nth-child(1) h3').textContent = visibleRows.length;

            // Calculate total amount from visible rows
            let totalAmount = 0;
            visibleRows.forEach(row => {
                const amountCell = row.querySelector('td[data-value]:nth-child(5)');
                if (amountCell) {
                    totalAmount += parseFloat(amountCell.getAttribute('data-value') || 0);
                }
            });

            // Update total amount stat
            const currencySymbol = '{{ currency_symbol }}';
            document.querySelector('.stat-card:nth-child(2) h3').textContent =
                currencySymbol + totalAmount.toFixed(2);

            // Count unique clients
            const uniqueClients = new Set();
            visibleRows.forEach(row => {
                uniqueClients.add(row.getAttribute('data-client'));
            });

            // Update unique clients stat
            document.querySelector('.stat-card:nth-child(3) h3').textContent = uniqueClients.size;
        };

        // Initialize filters
        initializeTableFilters('invoices-table', filterConfig);
    });

    // Confirm delete function for invoices
    function confirmDeleteInvoice(invoiceNumber) {
        if (confirm('Are you sure you want to delete invoice ' + invoiceNumber + '? This action cannot be undone.')) {
            window.location.href = '/delete_invoice/' + invoiceNumber;
        }
    }
</script>
{% endblock %}
