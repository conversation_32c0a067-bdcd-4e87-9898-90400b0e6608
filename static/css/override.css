/* 
 * Override styles for Invoice Generator 2025
 * This file contains styles that override the default styles
 */

/* Primary color overrides */
:root {
  --primary-override: #5a7d9a;
  --primary-hover-override: #4a6d8a;
}

/* Override button colors */
.btn-primary,
.action-btn-view,
a.btn-primary,
button[type="submit"],
.selection-button.selected {
  background-color: var(--primary-override) !important;
  border-color: var(--primary-override) !important;
  color: white !important;
}

.btn-primary:hover,
.action-btn-view:hover,
a.btn-primary:hover,
button[type="submit"]:hover {
  background-color: var(--primary-hover-override) !important;
  border-color: var(--primary-hover-override) !important;
}

.action-btn-delete {
  background-color: #e63946 !important;
}

/* Override checkboxes */
.custom-checkbox input:checked ~ .checkmark {
  background-color: var(--primary-override) !important;
  border-color: var(--primary-override) !important;
}

/* Override focus effects */
.form-control:focus {
  border-color: var(--primary-override) !important;
  box-shadow: 0 0 0 0.2rem rgba(90, 125, 154, 0.25) !important;
}

/* Override table header */
.table thead th {
  background-color: rgba(90, 125, 154, 0.1) !important;
}

/* Override app title */
.app-title {
  color: #2c6bed !important;
}

.app-title::after {
  background: linear-gradient(90deg, #2c6bed, #5a7d9a) !important;
}
