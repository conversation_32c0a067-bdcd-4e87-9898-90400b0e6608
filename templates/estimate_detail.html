{% extends "base.html" %}

{% block title %}Estimate {{ estimate.estimate_number }}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="card">
        <div class="card-header">
            <h2 class="mb-0">Estimate {{ estimate.estimate_number }}</h2>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h4>Client Details:</h4>
                    <p><strong>Name:</strong> {{ estimate.client_name }}</p>
                    <p><strong>Email:</strong> {{ estimate.client_email }}</p>
                    <p><strong>Phone:</strong> {{ estimate.client_phone }}</p>
                    <p><strong>Address:</strong> {{ estimate.client_address }}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h4>Estimate Details:</h4>
                    <p><strong>Estimate Number:</strong> {{ estimate.estimate_number }}</p>
                    <p><strong>Issue Date:</strong> {{ estimate.issue_date }}</p>
                    <p><strong>Valid Until:</strong> {{ estimate.valid_until if estimate.valid_until else 'N/A' }}</p>
                    <p><strong>Status:</strong> {{ estimate.status }}</p>
                </div>
            </div>

            <hr>

            <h4>Service Details:</h4>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th>Description</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ estimate.service_name }}</td>
                        <td>{{ estimate.service_description }}</td>
                        <td>{{ estimate.quantity }}</td>
                        <td>{{ estimate.currency_symbol }}{{ "%.2f"|format(estimate.unit_price) }}</td>
                        <td>{{ estimate.currency_symbol }}{{ "%.2f"|format(estimate.subtotal) }}</td>
                    </tr>
                </tbody>
            </table>

            <hr>

            <div class="row">
                <div class="col-md-7">
                    {% if estimate.notes %}
                        <h4>Notes:</h4>
                        <p>{{ estimate.notes }}</p>
                    {% endif %}
                    {% if estimate.terms %}
                        <h4>Terms & Conditions:</h4>
                        <p>{{ estimate.terms }}</p>
                    {% endif %}
                </div>
                <div class="col-md-5">
                    <div class="text-end">
                        <p><strong>Subtotal:</strong> {{ estimate.currency_symbol }}{{ "%.2f"|format(estimate.subtotal) }}</p>
                        {% if estimate.apply_iva %}
                        <p><strong>IVA ({{ config.IVA_RATE * 100 }}%):</strong> {{ estimate.currency_symbol }}{{ "%.2f"|format(estimate.iva_amount) }}</p>
                        {% endif %}
                        {% if estimate.apply_irpf %}
                        <p><strong>IRPF ({{ (estimate.irpf_rate * 100)|round(2) }}%):</strong> -{{ estimate.currency_symbol }}{{ "%.2f"|format(estimate.irpf_amount) }}</p>
                        {% endif %}
                        <hr>
                        <h4><strong>Total:</strong> {{ estimate.currency_symbol }}{{ "%.2f"|format(estimate.total_amount) }}</h4>
                    </div>
                </div>
            </div>
            
            <hr>

            <div class="text-center mt-4">
                <a href="{{ url_for('index') }}" class="btn btn-secondary">Back to Home</a>
                <a href="#" class="btn btn-primary">Print Estimate</a> 
                <a href="#" class="btn btn-success">Convert to Invoice</a>
                <button class="btn btn-danger" onclick="confirmDelete('{{ url_for("delete_estimate", estimate_number=estimate.estimate_number) }}', 'estimate')">Delete Estimate</button>
            </div>
        </div>
    </div>
</div>

<script>
// Using existing confirmDelete from main.js
// function confirmDelete(url, type) {
//     if (confirm(`Are you sure you want to delete this ${type}?`)) {
//         window.location.href = url;
//     }
// }
</script>
{% endblock %}