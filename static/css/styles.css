/*
 * Invoice Generator 2025
 * Modern, minimalist UI with enhanced features
 */

:root {
  /* Color palette - Light mode */
  --primary-color: #3a86ff;
  --primary-color-rgb: 58, 134, 255;
  --secondary-color: #8338ec;
  --secondary-color-rgb: 131, 56, 236;
  --accent-color: #ff006e;
  --accent-color-rgb: 255, 0, 110;
  --success-color: #38b000;
  --success-color-rgb: 56, 176, 0;
  --warning-color: #ffbe0b;
  --warning-color-rgb: 255, 190, 11;
  --danger-color: #ff5252;
  --danger-color-rgb: 255, 82, 82;
  --info-color: #0dcaf0;
  --info-color-rgb: 13, 202, 240;

  /* Neutral colors - Light mode */
  --background-color: #f0f2f5;
  --card-background: #ffffff;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border-color: #dee2e6;

  /* Dark mode colors - Will be applied when .dark-mode class is added to body */
  --dark-background-color: #121212;
  --dark-card-background: #1e1e1e;
  --dark-text-primary: #e0e0e0;
  --dark-text-secondary: #a0a0a0;
  --dark-border-color: #333333;

  /* Shadows and effects */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

  /* Neumorphism shadows */
  --neu-shadow-light: 5px 5px 10px rgba(166, 180, 200, 0.7);
  --neu-shadow-dark: -5px -5px 10px rgba(255, 255, 255, 0.9);
  --neu-shadow-pressed-light: inset 2px 2px 5px rgba(166, 180, 200, 0.7);
  --neu-shadow-pressed-dark: inset -2px -2px 5px rgba(255, 255, 255, 0.9);

  /* Glassmorphism effects */
  --glass-background: rgba(255, 255, 255, 0.25);
  --glass-border: 1px solid rgba(255, 255, 255, 0.18);
  --glass-blur: blur(10px);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);

  /* Animation and transitions */
  --transition-speed: 0.3s;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-size-base: 1rem;
  --font-size-sm: 0.875rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.5rem;
  --font-size-xxl: 2rem;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --line-height: 1.6;
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height);
  color: var(--text-primary);
  background-color: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding: 1.5rem;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5rem;
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
}

h1 {
  font-size: var(--font-size-xxl);
}

h2 {
  font-size: var(--font-size-xl);
}

h3 {
  font-size: var(--font-size-lg);
}

p {
  margin-bottom: 1rem;
}

/* App Header */
.app-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem 0;
}

.app-title {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  position: relative;
  display: inline-block;
}

.app-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

/* Bento Grid Layout */
.bento-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-auto-rows: minmax(100px, auto);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.bento-item {
  border-radius: var(--border-radius);
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-speed) ease;
}

/* Neumorphic Bento Items */
.bento-item {
  background-color: var(--background-color);
  box-shadow: var(--neu-shadow-light), var(--neu-shadow-dark);
  border-radius: var(--border-radius-lg);
  border: none;
}

.bento-item:hover {
  transform: translateY(-5px);
}

.bento-item .card-header {
  background-color: var(--background-color);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Glass Morphism for specific elements */
.details-panel {
  background: var(--glass-background);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: var(--border-radius);
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
  padding: 1.25rem;
  margin-top: 1rem;
  transition: all var(--transition-speed) ease;
}

/* Buttons with Neumorphism */
.btn {
  display: inline-block;
  font-weight: var(--font-weight-medium);
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  padding: 0.75rem 1.5rem;
  font-size: var(--font-size-base);
  line-height: 1.5;
  border-radius: var(--border-radius);
  transition: all var(--transition-speed) ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  color: white;
  background-color: var(--primary-color);
  border: none;
  box-shadow: 0 4px 6px rgba(58, 134, 255, 0.3);
}

.btn-primary:hover {
  background-color: #2a75e8;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(58, 134, 255, 0.4);
}

.btn-primary:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(58, 134, 255, 0.4);
}

/* Selection Buttons with Neumorphism */
.selection-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 150px;
  padding: 0.75rem 1.25rem;
  background-color: var(--background-color);
  border: none;
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  box-shadow: var(--neu-shadow-light), var(--neu-shadow-dark);
}

.selection-button:hover {
  transform: translateY(-2px);
}

.selection-button.selected {
  background-color: var(--primary-color);
  color: white;
  box-shadow: var(--neu-shadow-pressed-light), var(--neu-shadow-pressed-dark);
  transform: translateY(0);
}

/* Form Controls */
.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--background-color);
  background-clip: padding-box;
  border: none;
  border-radius: var(--border-radius);
  transition: all var(--transition-speed) ease;
  box-shadow: var(--neu-shadow-pressed-light), var(--neu-shadow-pressed-dark);
}

.form-control:focus {
  outline: none;
  box-shadow: var(--neu-shadow-pressed-light), var(--neu-shadow-pressed-dark), 0 0 0 3px rgba(58, 134, 255, 0.25);
}

/* Tables */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
  background: var(--glass-background);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  background-color: rgba(58, 134, 255, 0.1);
  text-align: left;
}

.table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Action Buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: var(--border-radius);
  padding: 0.4rem 0.75rem;
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: all var(--transition-speed) ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn-view {
  background-color: var(--primary-color);
}

.action-btn-view:hover {
  background-color: #2a75e8;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(58, 134, 255, 0.3);
}

.action-btn-delete {
  background-color: var(--danger-color);
}

.action-btn-delete:hover {
  background-color: #e03a3a;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(255, 82, 82, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

/* Dark Mode Styles */
body.dark-mode {
  background-color: var(--dark-background-color);
  color: var(--dark-text-primary);
}

body.dark-mode .bento-item {
  background-color: var(--dark-card-background);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode .card-header {
  background-color: var(--dark-card-background);
  border-bottom: 1px solid var(--dark-border-color);
}

body.dark-mode .card-title {
  color: var(--dark-text-primary);
}

body.dark-mode .form-control {
  background-color: var(--dark-background-color);
  color: var(--dark-text-primary);
  border-color: var(--dark-border-color);
}

body.dark-mode .selection-button {
  background-color: var(--dark-card-background);
  color: var(--dark-text-primary);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode .selection-button:hover {
  background-color: #2a2a2a;
}

body.dark-mode .selection-button.selected {
  background-color: var(--primary-color);
  color: white;
}

body.dark-mode .table {
  color: var(--dark-text-primary);
  background: rgba(30, 30, 30, 0.7);
}

body.dark-mode .table th {
  background-color: rgba(58, 134, 255, 0.2);
  color: var(--dark-text-primary);
}

body.dark-mode .table td {
  border-bottom: 1px solid var(--dark-border-color);
}

body.dark-mode .table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Dark Mode Toggle */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--card-background);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-speed) ease;
}

body.dark-mode .theme-toggle {
  background-color: var(--dark-card-background);
}

.theme-toggle i {
  font-size: 1.5rem;
  color: var(--text-primary);
  transition: all var(--transition-speed) ease;
}

body.dark-mode .theme-toggle i {
  color: var(--dark-text-primary);
}

.theme-toggle:hover {
  transform: rotate(15deg);
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.page-title h1 {
  margin: 0;
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 700;
}

.page-title h1 i {
  margin-right: 0.5rem;
  color: var(--primary-color);
}

.page-subtitle {
  margin: 0.5rem 0 0 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.page-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Badges */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.badge-invoice {
  color: white;
  background-color: var(--primary-color);
}

.badge-estimate {
  color: white;
  background-color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 992px) {
  .bento-grid {
    gap: 1rem;
  }

  .bento-item-lg {
    grid-column: span 12;
  }

  .bento-item-md, .bento-item-sm {
    grid-column: span 6;
  }
}

@media (max-width: 768px) {
  .bento-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .app-title {
    font-size: 1.75rem;
  }

  .selection-button {
    min-width: 100%;
  }

  .theme-toggle {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 576px) {
  body {
    padding: 1rem;
  }

  .container {
    padding: 0 0.5rem;
  }

  .app-title {
    font-size: 1.5rem;
  }

  .btn {
    padding: 0.6rem 1.25rem;
  }
}
