{% extends "base.html" %}

{% block title %}All Estimates - Invoice Generator 2025{% endblock %}

{% block extra_styles %}
/* Filter controls */
.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.filter-group select, .filter-group input {
    padding: 0.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    background-color: var(--input-background);
    color: var(--text-primary);
}

/* Summary stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--accent-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
}

.stat-content p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Table styles */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    font-weight: 600;
    color: var(--text-secondary);
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(var(--accent-color-rgb), 0.05);
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.action-btn i {
    margin-right: 0.25rem;
}

.action-btn-view {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
}

.action-btn-view:hover {
    background-color: var(--primary-color);
    color: white;
}

.action-btn-delete {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    color: var(--danger-color);
}

.action-btn-delete:hover {
    background-color: var(--danger-color);
    color: white;
}

/* Sortable table */
.sortable {
    cursor: pointer;
    position: relative;
    user-select: none;
}

.sortable i {
    margin-left: 5px;
    font-size: 0.8em;
    opacity: 0.5;
    transition: all 0.2s ease;
}

.sortable.asc i {
    opacity: 1;
    transform: rotate(180deg);
}

.sortable.desc i {
    opacity: 1;
    transform: rotate(0deg);
}

.sortable:hover i {
    opacity: 0.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
    }
    
    .summary-stats {
        grid-template-columns: 1fr;
    }
    
    .mobile-hide {
        display: none;
    }
}
{% endblock %}

{% block content %}
<!-- Year selector -->
<div class="year-selector mb-3">
    <label for="year-select">Select Year:</label>
    <select id="year-select" class="form-select d-inline-block w-auto" onchange="window.location.href='{{ url_for("estimates") }}?year=' + this.value">
        {% for year in available_years %}
        <option value="{{ year }}" {% if year == selected_year %}selected{% endif %}>{{ year }}</option>
        {% endfor %}
    </select>
</div>

<!-- Summary stats -->
<div class="summary-stats">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-file-alt"></i> <!-- Changed icon for estimates -->
        </div>
        <div class="stat-content">
            <h3>{{ estimates|length }}</h3>
            <p>Total Estimates</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-tasks"></i> <!-- Icon for status -->
        </div>
        <div class="stat-content">
            <h3>{{ estimates_by_status.Draft|default(0) }} Draft / {{ estimates_by_status.Sent|default(0) }} Sent</h3>
            <p>Status Overview</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
            <h3>{{ unique_clients }}</h3>
            <p>Unique Clients</p>
        </div>
    </div>
</div>

<!-- Filter controls -->
<div class="filter-controls">
    <div class="filter-group">
        <label for="client-filter">Filter by Client:</label>
        <select id="client-filter" class="form-select">
            <option value="all">All Clients</option>
            {% for client_name in client_names %}
            <option value="{{ client_name }}">{{ client_name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="filter-group">
        <label for="month-filter">Filter by Month:</label>
        <select id="month-filter" class="form-select">
            <option value="all">All Months</option>
            {% for month_num, month_name in months.items() %}
            <option value="{{ month_num }}">{{ month_name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="filter-group">
        <label for="status-filter">Filter by Status:</label>
        <select id="status-filter" class="form-select">
            <option value="all">All Statuses</option>
            {% for status in statuses %}
            <option value="{{ status }}">{{ status }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="filter-group">
        <label for="search-filter">Search:</label>
        <input type="text" id="search-filter" class="form-control" placeholder="Search estimates...">
    </div>
</div>

<!-- Estimates table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table sortable-table" id="estimates-table">
                <thead>
                    <tr>
                        <th class="sortable" data-sort="estimate_number">Estimate # <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="issue_date">Issue Date <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="client_name">Client <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="service_name">Service <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="total_amount">Amount <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="status">Status <i class="fas fa-sort"></i></th>
                        <th class="sortable" data-sort="valid_until">Valid Until <i class="fas fa-sort"></i></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for estimate in estimates %}
                    <tr data-client="{{ estimate.client_name }}" data-month="{{ estimate.issue_date.split('-')[1] }}" data-status="{{ estimate.status }}">
                        <td data-value="{{ estimate.estimate_number }}"><a href="{{ url_for('view_estimate', estimate_number=estimate.estimate_number) }}">{{ estimate.estimate_number }}</a></td>
                        <td data-value="{{ estimate.issue_date }}" data-date="{{ estimate.issue_date.replace('-','') }}">{{ estimate.issue_date }}</td>
                        <td data-value="{{ estimate.client_name }}">{{ estimate.client_name }}</td>
                        <td data-value="{{ estimate.service_name }}">{{ estimate.service_name }}</td>
                        <td data-value="{{ estimate.total_amount }}">
                            {{ estimate.currency_symbol }}{{ "%.2f"|format(estimate.total_amount) }}
                        </td>
                        <td data-value="{{ estimate.status }}">{{ estimate.status }}</td>
                        <td data-value="{{ estimate.valid_until if estimate.valid_until else '' }}">{{ estimate.valid_until if estimate.valid_until else 'N/A' }}</td>
                        <td>
                            <div class="table-actions">
                                <a href="{{ url_for('view_estimate', estimate_number=estimate.estimate_number) }}" class="action-btn action-btn-view">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <button class="action-btn action-btn-delete" onclick="confirmDelete('{{ url_for("delete_estimate", estimate_number=estimate.estimate_number) }}', 'estimate')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center">No estimates found for the selected year.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clientFilter = document.getElementById('client-filter');
        const monthFilter = document.getElementById('month-filter');
        const statusFilter = document.getElementById('status-filter');
        const searchFilter = document.getElementById('search-filter');
        const table = document.getElementById('estimates-table');
        const rows = table.querySelectorAll('tbody tr');
        
        function applyFilters() {
            const clientValue = clientFilter.value;
            const monthValue = monthFilter.value;
            const statusValue = statusFilter.value;
            const searchValue = searchFilter.value.toLowerCase();
            
            let visibleCount = 0;
            rows.forEach(row => {
                if (row.cells.length < 2) return; // Skip placeholder rows if any
                const clientMatch = clientValue === 'all' || row.getAttribute('data-client') === clientValue;
                const monthMatch = monthValue === 'all' || row.getAttribute('data-month') === monthValue;
                const statusMatch = statusValue === 'all' || row.getAttribute('data-status') === statusValue;
                
                let searchMatch = true;
                if (searchValue) {
                    searchMatch = Array.from(row.cells).some(cell => 
                        cell.textContent.toLowerCase().includes(searchValue)
                    );
                }
                
                if (clientMatch && monthMatch && statusMatch && searchMatch) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            updateFilteredStats(visibleCount);
        }

        function updateFilteredStats(visibleCount) {
            document.querySelector('.summary-stats .stat-card:nth-child(1) .stat-content h3').textContent = visibleCount;
            // Add more stat updates if needed (e.g., total amount of filtered estimates)
        }
        
        if(clientFilter) clientFilter.addEventListener('change', applyFilters);
        if(monthFilter) monthFilter.addEventListener('change', applyFilters);
        if(statusFilter) statusFilter.addEventListener('change', applyFilters);
        if(searchFilter) searchFilter.addEventListener('input', applyFilters);

        // Initial application of filters
        applyFilters();

        // Sorting logic
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const thTable = header.closest('table');
                const thTbody = thTable.querySelector('tbody');
                const column = header.dataset.sort;
                const currentDirection = header.classList.contains('asc') ? 'asc' : (header.classList.contains('desc') ? 'desc' : '');
                let direction;

                // Reset other headers' sort indicators and classes
                header.parentElement.querySelectorAll('.sortable').forEach(th => {
                    if (th !== header) {
                        th.classList.remove('asc', 'desc');
                        th.querySelector('i').className = 'fas fa-sort';
                    }
                });

                if (currentDirection === 'asc') {
                    direction = 'desc';
                    header.classList.remove('asc');
                    header.classList.add('desc');
                    header.querySelector('i').className = 'fas fa-sort-down';
                } else { // Covers 'desc' and no direction
                    direction = 'asc';
                    header.classList.remove('desc');
                    header.classList.add('asc');
                    header.querySelector('i').className = 'fas fa-sort-up';
                }
                
                Array.from(thTbody.querySelectorAll('tr'))
                    .sort((a, b) => {
                        let valA = a.querySelector(`td[data-value='${column}']`)?.dataset.value || 
                                   a.querySelector(`td[data-sort='${column}']`)?.dataset.value || // Check data-sort as well
                                   a.querySelector(`td:nth-child(${header.cellIndex + 1})`)?.dataset.value || // Fallback to cell index based data-value
                                   a.querySelector(`td:nth-child(${header.cellIndex + 1})`)?.textContent.trim(); // Fallback to text content
                        
                        let valB = b.querySelector(`td[data-value='${column}']`)?.dataset.value || 
                                   b.querySelector(`td[data-sort='${column}']`)?.dataset.value ||
                                   b.querySelector(`td:nth-child(${header.cellIndex + 1})`)?.dataset.value ||
                                   b.querySelector(`td:nth-child(${header.cellIndex + 1})`)?.textContent.trim();

                        // Special handling for dates
                        if (header.dataset.sort === 'issue_date' || header.dataset.sort === 'valid_until') {
                            valA = a.querySelector(`td:nth-child(${header.cellIndex + 1})`)?.dataset.date || '0';
                            valB = b.querySelector(`td:nth-child(${header.cellIndex + 1})`)?.dataset.date || '0';
                        } else if (header.dataset.sort === 'total_amount' || header.dataset.sort === 'estimate_number') { // Numeric sort for amount and numbers
                             valA = parseFloat(valA.replace(/[^0-9.-]+/g,"")) || 0;
                             valB = parseFloat(valB.replace(/[^0-9.-]+/g,"")) || 0;
                        } else { // String sort for others
                            valA = String(valA).toLowerCase();
                            valB = String(valB).toLowerCase();
                        }

                        if (valA < valB) return direction === 'asc' ? -1 : 1;
                        if (valA > valB) return direction === 'asc' ? 1 : -1;
                        return 0;
                    })
                    .forEach(tr => thTbody.appendChild(tr));
            });
        });
    });

    // confirmDelete function is expected to be in main.js
    // function confirmDelete(url, type) {
    //     if (confirm(`Are you sure you want to delete this ${type}? This action cannot be undone.`)) {
    //         window.location.href = url;
    //     }
    // }
</script>
{% endblock %}